<script>
import { onMounted } from "vue";
import { useTheme } from "@/composables/useTheme";
import Navbar from "./components/Navbar.vue";
import Footer from "./components/Footer.vue";
import RecipeModal from "./components/RecipeModal.vue";

export default {
  name: "App",
  components: {
    Navbar,
    Footer,
    RecipeModal,
  },
  setup() {
    const { initializeTheme, watchSystemTheme } = useTheme();

    onMounted(() => {
      initializeTheme();
      watchSystemTheme();
    });

    return {};
  },
};
</script>

<template>
  <div id="app-layout">
    <!-- Navigation Header -->
    <header class="app-header">
      <Navbar />
    </header>

    <!-- Main Content Area -->
    <main class="app-main">
      <div class="main-content">
        <RouterView />
      </div>
    </main>

    <!-- Footer -->
    <footer class="app-footer">
      <Footer />
    </footer>

    <!-- Modal Components -->
    <RecipeModal />
  </div>
</template>

<style scoped>
/* App Layout Structure */
#app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: var(--bg-color-light);
  transition: background-color var(--transition-medium);
}

/* Header Styles */
.app-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: var(--nav-bg);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: var(--shadow-light);
  transition: all var(--transition-medium);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.app-header:hover {
  box-shadow: var(--shadow-medium);
}

/* Main Content Area */
.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Important for flex children */
  position: relative;
}

.main-content {
  flex: 1;
  width: 100%;
  padding: 0;
  margin: 0;
  min-height: calc(100vh - var(--header-height) - 200px);
  display: flex;
  flex-direction: column;
}

/* Footer Styles */
.app-footer {
  margin-top: auto; /* This pushes the footer to the bottom */
  flex-shrink: 0; /* Prevents footer from shrinking */
  position: relative;
  z-index: 10;
}

/* Loading and Error States */
.app-main.loading {
  justify-content: center;
  align-items: center;
}

.app-main.error {
  justify-content: center;
  align-items: center;
  background-color: rgba(244, 67, 54, 0.05);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-content {
    min-height: calc(100vh - 180px);
  }
}

@media (max-width: 768px) {
  .main-content {
    min-height: calc(100vh - 150px);
  }

  .app-header {
    position: relative; /* Remove sticky on mobile for better UX */
    box-shadow: var(--shadow-light);
  }

  .app-header:hover {
    box-shadow: var(--shadow-light); /* Reduce hover effect on mobile */
  }
}

@media (max-width: 480px) {
  .main-content {
    min-height: calc(100vh - 120px);
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .app-header {
    border-bottom-width: 0.5px;
  }
}

/* Dark Mode Support (if implemented later) */
@media (prefers-color-scheme: dark) {
  #app-layout {
    background-color: #1a1a1a;
  }

  .app-header {
    /* background-color: #1a1a1a; */
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
}

/* Focus management for accessibility */
.app-main:focus {
  outline: none;
}

.app-main:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .app-header,
  .app-footer {
    display: none !important;
  }

  .app-main {
    min-height: auto;
    flex: none;
  }

  #app-layout {
    min-height: auto;
    background: white;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .app-header {
    transition: none;
  }
}
</style>
