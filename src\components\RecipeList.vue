<template>
  <div class="recipe-grid">
    <RecipeCard
      v-for="recipe in recipes"
      :key="recipe.id"
      :recipe="recipe"
      @recipe-click="handleRecipeClick"
    />
  </div>
</template>

<script>
import RecipeCard from "@/components/RecipeCard.vue";

export default {
  name: "RecipeList",
  components: {
    RecipeCard,
  },
  props: {
    recipes: {
      type: Array,
      required: true,
    },
  },
  emits: ["recipe-click"],
  setup(props, { emit }) {
    const handleRecipeClick = (recipeId) => {
      emit("recipe-click", recipeId);
    };

    return { handleRecipeClick };
  },
};
</script>

<style>
.recipe-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.section-heading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-heading a {
  color: var(--primary-color);
  transition: color 0.4s ease;
}

.section-heading a:hover {
  color: var(--secondary-color-light);
}

.heading-text h2 {
  font-size: 1.6rem;
  text-transform: capitalize;
  color: black;
  font-family: "Roboto", sans-serif;
  font-weight: 500;
  margin-bottom: 0.5rem;
}
</style>
