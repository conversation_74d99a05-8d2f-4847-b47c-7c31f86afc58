<template>
  <Container>
    <header class="header">
      <div class="hero">
        <div class="hero-content">
          <h1>The Easiest Way to Make Your Favorite Meals</h1>
          <p>
            Discover a world of 1000+ recipes, from classic comfort foods to
            exotic dishes. Help you to find and make your favorite meals.
          </p>
          <button class="btn btn-primary" @click="navigateToRecipe">
            Explore Recipes
          </button>
        </div>
        <div class="hero-image">
          <img class="hero-dish" :src="dish" alt="Dish Image" />
        </div>
      </div>
    </header>
  </Container>
</template>

<script>
import Logo from "./icons/Logo.vue";
import Container from "./Container.vue";
import Icon from "./Icon.vue";
import dish from "../assets/images/dishe.png";
import { ref } from "vue";
import { useRouter } from "vue-router";

export default {
  name: "Header",
  components: {
    Container,
    Icon,
    Logo,
  },

  setup() {
    const router = useRouter();
    const dishRef = ref(dish);
    const navigateToRecipe = () => {
      router.push({ name: "Recipes" });
    };

    return {
      dish: dishRef,
      navigateToRecipe,
    };
  },
};
</script>

<style lang="scss">
$primary-color: #e45700;
$primary-color-light: #bc6128;
$primary-color-dark: #1c1c1c;
$color-light: #f1dfd4c5;

@mixin button-style {
  display: inline-block;
  padding: 10px 20px;
  font-size: 15px;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  border: none;
  border-radius: 4px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.header {
  .hero {
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-content {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      gap: 20px;
      width: 50%;

      h1 {
        font-size: 45px;
        font-weight: 700;
        letter-spacing: 1px;
        color: #1c1c1c;
        width: 65%;
        text-transform: capitalize;
      }
      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 1.5;
        color: #1c1c1c;
        width: 65%;
      }
    }

    .btn {
      @include button-style;
      &-primary {
        background-color: $primary-color;
        color: #fff;

        &:hover {
          background-color: $primary-color-light;
          box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: translateY(1px);
        }
      }
    }

    &-image {
      width: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &-dish {
      width: 550px;
      object-fit: cover;
    }
  }
}
</style>
