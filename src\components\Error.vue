<template>
  <div class="error-component">
    <svg
      class="error-icon"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="12" y1="8" x2="12" y2="12"></line>
      <line x1="12" y1="16" x2="12.01" y2="16"></line>
    </svg>
    <p class="error-message">{{ errorMessage }}</p>
  </div>
</template>

<script>
export default {
  name: "ErrorComponent",
  props: {
    errorMessage: {
      type: String,
      required: true,
    },
  },
};
</script>
<style>
.error-component {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.error-icon,
.error-message {
  color: #a00;
}

.error-icon {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

.error-message {
  color: #a00;
  font-size: 1.2rem;
  font-weight: 500;
}
</style>
