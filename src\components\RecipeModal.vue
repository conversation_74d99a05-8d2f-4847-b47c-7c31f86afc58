<!-- RecipeModal.vue -->
<template>
  <Transition name="modal-fade">
    <div class="modal-overlay" @click="closeModal" v-if="showModal">
      <div class="modal-content" @click.stop>
        <button class="close-button" @click="closeModal">&times;</button>
        <h2 class="modal-title">{{ recipe.title }}</h2>
        <img :src="recipe.image" :alt="recipe.title" class="modal-image" />
        <p class="modal-description">{{ recipe.description }}</p>
        <!-- Add more fields from recipeData as needed -->
      </div>
    </div>
  </Transition>
</template>

<script>
import { defineEmits } from "vue";
export default {
  name: "RecipeModal",
  props: {
    recipe: {
      type: Object,
      required: true,
    },
    showModal: {
      type: Boolean,
      required: true,
    },
  },
  emits: defineEmits(["close"]),
  setup(props, { emit }) {
    const closeModal = () => {
      emit("close"); // Emit the 'close' event
    };
    return { closeModal };
  },
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  max-width: 600px;
  width: 90%;
  position: relative;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 24px;
  cursor: pointer;
  border: none;
  background: none;
  color: #333;
}

.modal-image {
  max-width: 100%;
  height: auto;
  margin-bottom: 10px;
}

.modal-title {
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.modal-description {
  font-size: 1rem;
  line-height: 1.5;
}

.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-content {
  transition: transform 0.3s ease;
}

.modal-fade-enter-from .modal-content,
.modal-fade-leave-to .modal-content {
  transform: scale(0.8);
}
</style>
