<template>
  <div class="recipe-card">
    <RouterLink :to="{ name: 'RecipeDetail', params: { id: recipe.id } }">
      <div class="recipe-image-container">
        <img :src="recipe.image" :alt="recipe.title" loading="lazy" />
      </div>
      <div class="recipe-card-body">
        <h3 class="recipe-card-title">{{ recipe.title }}</h3>
        <p class="muted recipe-card-description">{{ recipe.description }}</p>
      </div>
      <div class="recipe-card-footer"></div>
    </RouterLink>
  </div>
</template>

<script>
export default {
  name: "RecipeCard",
  props: {
    recipe: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style>
.recipe-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.recipe-card {
  width: 300px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.recipe-card:hover {
  transform: translateY(-5px);
}

.recipe-card a {
  text-decoration: none;
  color: inherit;
}

.recipe-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.recipe-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.recipe-image-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.recipe-card:hover .recipe-image-container::after {
  opacity: 1;
}

.recipe-card:hover .recipe-image-container img {
  transform: scale(1.05);
}

.recipe-card-body {
  padding: 16px;
}

.recipe-card-title {
  font-size: 1.25rem;
  margin-bottom: 8px;
}

.recipe-card-description {
  font-size: 0.9rem;
  color: #666;
}

.recipe-card-footer {
  padding: 12px 16px;
  border-top: 1px solid #eee;
}
</style>
