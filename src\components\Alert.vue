<template>
  <div class="alert" :class="alertType">
    {{ alertMessage }}
  </div>
</template>

<script>
export default {
  props: {
    alertMessage: {
      type: String,
      required: true,
    },
    alertType: {
      type: String,
      default: "info",
      validator: function (value) {
        return ["info", "success", "warning", "error"].includes(value);
      },
    },
  },
};
</script>

<style scoped>
.alert {
  padding: 1rem;
  border-radius: 5px;
  margin-bottom: 1rem;
}

.info {
  background-color: #d1ecf1;
  border-color: #67c2ef;
  color: #005693;
}

.success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.warning {
  background-color: #fff3cd;
  border-color: #ffc107;
  color: #856404;
}

.error {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}
</style>
