:root {
  --primary-color: #e45700;
  --primary-color-light: #bc6128;
  --secondary-color: #f5f5f5;
  --bg-color-light: #f1dfd4c5;
  --font-family: "Roboto", sans-serif;
  --base-font-size: 16px;
  --text-color: #2f2f2f;
  --text-muted: #b7b7b7;
  --bg-color: #ffffff;
  --nav-bg: #ffffff;
  --nav-text: #2f2f2f;
  --footer-bg: #1e4f4f;
  --footer-text: #d4d7dd;

  /* Layout Variables */
  --header-height: 80px;
  --footer-height: auto;
  --container-max-width: 1200px;
  --container-padding: 20px;
  --section-spacing: 40px;
  --border-radius: 8px;

  /* Shadow Variables */
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.15);

  /* Transition Variables */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.4s ease;
}

:root[data-theme="dark"] {
  --primary-color: #ff6b1a;
  --primary-color-light: #ff8c4d;
  --secondary-color: #2d2d2d;
  --bg-color: #1a1a1a;
  --bg-color-light: #1a1a1a;
  --text-color: #ffffff;
  --text-muted: #888888;
  --nav-bg: #2d2d2d;
  --nav-text: #ffffff;
  --footer-bg: #0f2f2f;
  --footer-text: #b4b7bd;

  /* Dark theme specific shadows */
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.4);
  --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.5);
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-weight: 400;
}

img {
  max-width: 100%;
  height: auto;
}

a {
  text-decoration: none;
  transition: 0.4s;
  display: inline-block;
}

ul {
  list-style: none;
}

html {
  box-sizing: border-box;
  scroll-behavior: smooth;
}

body {
  color: var(--text-color);
  background: var(--bg-color-light);
  font-family: var(--font-family);
  font-size: var(--base-font-size);
  line-height: 1.6;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  transition: background-color var(--transition-medium),
    color var(--transition-medium);
}

/* Layout Utilities */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
  width: 100%;
}

.section {
  margin: var(--section-spacing) 0;
}

/* Responsive Utilities */
@media (max-width: 768px) {
  :root {
    --container-padding: 16px;
    --section-spacing: 24px;
  }
}

@media (max-width: 480px) {
  :root {
    --container-padding: 12px;
    --section-spacing: 20px;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
