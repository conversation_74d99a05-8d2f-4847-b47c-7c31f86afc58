<!-- Here the recipes will be displayed as a route -->

<template>
  <div>
    <Container>
      <header class="section-heading">
        <div class="heading-text">
          <h2 class="section-title">Breakfast Recipes</h2>
          <p class="muted"></p>
        </div>
        <RouterLink :to="{ name: 'Recipes' }" class="see-all-link"
          >see all</RouterLink
        >
      </header>
      <div v-if="isLoading"><Loading /></div>
      <Error v-else-if="error" :errorMessage="error" />
      <RecipeList v-else :recipes="recipes" />
    </Container>
  </div>
</template>

<script>
import { onMounted } from "vue";
import useFetchRecipes from "@/composables/useFetchRecipes";
import Container from "@/components/Container.vue";

import RecipeList from "@/components/RecipeList.vue";
import Loading from "@/components/Loading.vue";
import Error from "@/components/Error.vue";

export default {
  name: "Recipes",
  components: {
    Container,
    RecipeList,
    Loading,
    Error,
  },
  setup() {
    const { recipes, error, isLoading, fetchRecipes } = useFetchRecipes();

    onMounted(() => fetchRecipes("breakfast"));

    return { recipes, error, isLoading };
  },
};
</script>

<style></style>
